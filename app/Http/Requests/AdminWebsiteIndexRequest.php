<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AdminWebsiteIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => 'nullable|in:active,inactive',
            'publisher_id' => 'nullable|exists:publishers,id',
            'topic_id' => 'nullable|exists:topics,id',
            'search' => 'nullable|string',
            'sort' => 'nullable|in:id,created_at',
            'order' => 'nullable|in:asc,desc',
            'page' => 'nullable|integer',
        ];
    }
}
