<?php

namespace App\Http\Controllers\Admin;

use App\Enums\Role;
use App\Models\User;
use Inertia\Inertia;
use App\Models\Topic;
use Inertia\Response;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use App\Services\Admin\AdminWebsiteService;
use Illuminate\Http\JsonResponse;
use App\Models\MarketplaceWebsite;
use App\Http\Controllers\Controller;
use App\Rules\ValidWebsiteEligibility;
use App\Models\MarketplaceAdminWebsite;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\AdminWebsiteIndexRequest;
use App\Http\Requests\AdminWebsiteStoreRequest;
use App\Http\Requests\AdminWebsiteUpdateRequest;
use App\Services\Domain\ExtractDomainNameService;
use App\Http\Controllers\MarketplaceMetaDataController;
use App\Notifications\Publisher\PublisherNewAccountNotification;
use Illuminate\Support\Collection;
use Domain\Website\Traits\WebsiteActionTrait;




class AdminWebsiteController extends Controller
{
    use WebsiteActionTrait;
    private readonly Collection $categories;
    private readonly Collection $languages;

    /*********************************************************************
     * CONSTRUCTOR ‼️🚨 ‼️🚨 ‼️🚨 ‼️🚨 ‼️🚨 ‼️🚨 ‼️🚨 ‼️🚨 
     *********************************************************************
     *
     * We have gates defined that we use as middleware but here
     * middleware is being used when gates are available and gates are 
     * the right way to do it !!!!!!
     * 
     * 
     * Initialize the ExtractDomainName service for domain extraction.
     * Apply middleware to protect all routes with manage-websites gate.
     *
     * @param ExtractDomainName $extractDomainName
     *********************************************************************/
    public function __construct(
        private ExtractDomainNameService $extractDomainName,
        private AdminWebsiteService $websiteService
    ) {

        $this->categories = MarketplaceMetaDataController::categoriesList();
        $this->languages = MarketplaceMetaDataController::languagesList();
    }



    /*********************************************************************
     * ADMIN WEBSITE LISTING & FILTERING (REFACTOR Centeral logic ‼️🚨)
     **********************************************************************
     *
     * Refactor this code:
     * - Too many responsibilities in one place: 
     * - One main listing function, with validation, filters and other 
     * logic defined in separate methods which is imported.
     * 
     * Displays a paginated list of marketplace websites with full support
     * for filtering, sorting, and role-based access customization.
     *
     * Key Features:
     * - Outreach users are redirected to the website form upon assignment.
     * - Filters include search term, status, verification, outreach status.
     * - Sorting by allowed fields like ID, domain, orders count, etc.
     *
     * If the user is an Outreach role:
     * - Redirects to assigned website edit form
     * - Defaults outreach filter to "inprogress"
     * - Displays count of unassigned websites
     *
     * @param Request $request
     * Incoming HTTP request with filters, sort fields, and pagination controls
     *
     * @return \Inertia\Response
     * Renders the websites index view with paginated and filtered websites
     *********************************************************************/

    public function index(AdminWebsiteIndexRequest $request): Response
    {
        // -----------------------
        // Get Results
        // -----------------------
        $results = $this->websiteService->index($request->validated());

        // -----------------------
        // Response
        // -----------------------
        return Inertia::render('Admin/Websites/Index', $results);
    }






    /*********************************************************************
     * ADMIN WEBSITE CREATE FORM
     **********************************************************************
     *
     * Prepares and renders the form used to create a new website entry.
     *
     * Loads all available website categories and languages to populate
     * dropdown options in the form. Identifies if the current user is
     * an Outreach user to adjust frontend behavior accordingly.
     *
     * @return \Inertia\Response
     * Renders the website creation form with necessary metadata
     *********************************************************************/

    public function create()
    {

        $user = auth('web')->user();

        return Inertia::render('Admin/Websites/WebsiteForm', [
            'categories' => $this->categories,
            'languages' => $this->languages,
            'mode' => 'create',
            'is_outreach' => $user->role === Role::Outreach->value,
            'is_publisher' => $user->role === Role::Publisher->value,
        ]);
    }





    /*********************************************************************
     * STORE NEW WEBSITE (REFACTOR - poorly coded ‼️)
     **********************************************************************
     *
     * Handles the submission of the website creation form.
     *
     * Validates and stores a new MarketplaceAdminWebsite. If the current
     * user is a Publisher, assigns them as the website owner. If the user
     * is Outreach, assigns the website to the outreach user with status
     * set to 'inprogress'.
     *
     * Also:
     * - Extracts and syncs topic tags.
     * - Redirects to appropriate dashboard (publisher or admin) on success.
     *
     * @param AdminWebsiteStoreRequest $request
     * Validated request containing website creation data
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects to either publisher or admin website edit page with success message
     *********************************************************************/
    public function store(AdminWebsiteStoreRequest $request)
    {

        $website = $this->storeWebsite($request->validated());

        // redirect to edit page
        if ($website) {
            return redirect()->route('admin.websites.edit', $website->id)
                ->with('success', 'Website created successfully.');
        }

        return redirect()->back()->with('error', 'Failed to create website.');
    }






    // /*********************************************************************
    //  * ADMIN WEBSITE SHOW FUNCTION
    //  **********************************************************************
    //  * 
    //  *
    //  *********************************************************************/
    // public function show(MarketplaceAdminWebsite $website): void
    // {
    //     //
    // }





    /*********************************************************************
     * EDIT WEBSITE FORM
     **********************************************************************
     *
     * Loads and displays the edit form for a given MarketplaceAdminWebsite.
     *
     * Access is restricted to:
     * - Admin users
     * - Publisher users who own the website
     *
     * Eager loads:
     * - Publisher (with website count)
     * - Outreach assignment
     * - SEO statistics
     * - Website language, category, and assigned topics
     *
     * Also retrieves:
     * - Top traffic country (based on SEO stats)
     * - Full lists of available categories and languages
     *
     * @param MarketplaceAdminWebsite $website
     * The website being edited
     *
     * @return \Inertia\Response
     * Renders the populated website form for editing
     *********************************************************************/

    public function edit(MarketplaceAdminWebsite $website)
    {
        $user = auth('web')->user();

        $website->load([
            'publisher',
            'seoStats.country',
            'language',
            'category',
            'topics'
        ]);


        return Inertia::render('Admin/Websites/WebsiteForm', [
            'website' => $website,
            'is_outreach' => $user->role === Role::Outreach->value,
            'is_publisher' => $user->role === Role::Publisher->value,
            'categories' => $this->categories,
            'languages' => $this->languages,
            'top_traffic_country' => $website->seoStats?->country ?? null,
            'mode' => 'edit',
            'topics' => $website->topics->pluck('name'),
        ]);
    }






    /*********************************************************************
     * UPDATE WEBSITE
     **********************************************************************
     *
     * Updates an existing MarketplaceAdminWebsite with validated data.
     *
     * Core Logic:
     * - Validates and updates website fields
     * - Automatically updates outreach status:
     *   - Sets status to 'onboarded' if website is verified and active
     *   - Sets status back to 'inprogress' otherwise
     * - Syncs related topics and logs topic changes if there are any differences
     *
     * Additional Features:
     * - Gracefully handles duplicate domain errors
     * - Redirects user to appropriate edit route based on their role (admin/publisher)
     *
     * @param AdminWebsiteUpdateRequest $request
     * Validated update request containing website data and topic tags
     *
     * @param MarketplaceAdminWebsite $website
     * The website to update
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects to the website edit page with success or validation error message
     *********************************************************************/
    public function update(AdminWebsiteUpdateRequest $request, MarketplaceAdminWebsite $website)
    {
        $result = $this->updateWebsite($request->validated(), $website);

        if ($result !== false) {
            return redirect()->route('admin.websites.edit', $website->id)
                ->with('success', 'Website updated successfully.');
        }

        return redirect()->back()->with('error', 'Failed to update website.');
    }






    /********************************************************************
     * ADMIN WEBSITE DELETE FUNCTION
     *********************************************************************
     *
     * Delete a specific marketplace admin website.
     *
     * Attempts to delete the given website and handles potential deletion errors.
     * Redirects to the websites index page with a success or error message.
     *
     * @param MarketplaceAdminWebsite $website The website to be deleted
     * @return \Illuminate\Http\RedirectResponse Redirect to the websites index page
     *
     ********************************************************************/
    public function destroy(MarketplaceAdminWebsite $website)
    {
        try {
            $website->delete();

            return redirect()
                ->route('admin.websites.index')
                ->with('success', 'Website deleted successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.websites.index')
                ->with('error', 'Failed to delete the website.');
        }
    }






    /*********************************************************************
     * REJECT OUTREACH STATUS
     **********************************************************************
     *
     * Rejects the outreach assignment of a specific website.
     *
     * Actions performed:
     * - Sets the website's `active` status to false (0)
     * - Updates the outreach status to 'rejected'
     * - Records the rejection timestamp (`rejected_at`)
     *
     * Typically used when a website fails to meet verification criteria
     * during the outreach process.
     *
     * @param Request $request
     * The HTTP request object (not directly used in this method)
     *
     * @param MarketplaceAdminWebsite $website
     * The website whose outreach status is to be rejected
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects back to the websites index with a success message
     *********************************************************************/

    public function rejectOutreachStatus(Request $request, MarketplaceAdminWebsite $website)
    {

        // set website active to 0
        $website->active = 0;
        $website->save();

        $website->outreach()->update([
            'status' => 'rejected',
            'rejected_at' => now(),
        ]);


        //--------------------------------
        // redirect to index 
        return redirect()->route('admin.websites.index')
            ->with('success', 'Outreach status updated successfully.');
    }





    /********************************************************************
     * ADMIN WEBSITE ACTIVE TOGGLE FUNCTION
     *********************************************************************
     * Toggle the active status of a marketplace admin website.
     *
     * Updates the 'active' status of the given website based on the request input
     * and returns back to the previous page with a success message.
     *
     * @param Request $request The incoming HTTP request
     * @param MarketplaceAdminWebsite $website The website to update
     * @return \Illuminate\Http\RedirectResponse
     ********************************************************************/
    public function toggleStatus(Request $request, MarketplaceAdminWebsite $website)
    {

        $website->update([
            'active' => $request->boolean('active'),
        ]);

        $website->outreach()->update([
            'status' => $request->boolean('active') ? 'onboarded' : 'inprogress',
        ]);

        return back()->with('success', 'Website status updated successfully.');
    }






    /********************************************************************
     * ADMIN WEBSITE SEARCH PUBLISHERS
     *********************************************************************
     * Search for publishers based on optional search criteria.
     *
     * Retrieves a list of up to 10 publishers matching the search query,
     * filtered by name or email. Returns publisher details including
     * id, name, and email, sorted by most recent first.
     *
     * @param Request $request The HTTP request containing optional 
     * search parameters
     * 
     * @return \Illuminate\Database\Eloquent\Collection
     ********************************************************************/
    public function searchPublishers(Request $request)
    {

        $search = $request->input('searchTerm', '');

        return User::where('role', Role::Publisher->value)
            ->when($search, function ($publisherQuery) use ($search) {
                $publisherQuery->where(function ($searchFilter) use ($search) {
                    $searchFilter->where('name', 'like', "%$search%")
                        ->orWhere('email', 'like', "%$search%");
                });
            })
            ->orderBy('id', 'desc')
            ->take(10)
            ->get(['id', 'name', 'email']);
    }







    /********************************************************************
     * ADMIN WEBSITE ASSIGN PUBLISHER TO WEBSITE
     *********************************************************************
     *
     * Assigns a publisher to a marketplace admin website.
     *
     * Validates the provided user ID and updates the website's publisher_user_id.
     * Returns a JSON response indicating successful publisher assignment.
     *
     * @param Request $request The HTTP request containing the user ID to assign
     * @param MarketplaceAdminWebsite $website The website to assign the publisher to
     * @return JsonResponse
     ********************************************************************/
    public function assignPublisher(Request $request, MarketplaceAdminWebsite $website)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $website->update([
            'publisher_user_id' => $request->user_id,
        ]);

        return response()->json(['message' => 'Publisher assigned successfully.']);
    }






    /********************************************************************
     * ADMIN WEBSITE UNASSIGN PUBLISHER FROM WEBSITE
     ********************************************************************
     * 
     * Removes the publisher assignment from a marketplace admin website.
     *
     * Clears the publisher_user_id by setting it to 0, effectively unassigning
     * the current publisher from the website.
     *
     * @param Request $request The HTTP request (unused in this method)
     * @param MarketplaceAdminWebsite $website The website to unassign the publisher from
     * @return JsonResponse
     ********************************************************************/
    public function unassignPublisher(Request $request, MarketplaceAdminWebsite $website)
    {

        $website->update([
            'publisher_user_id' => 0,
        ]);

        return response()->json(['message' => 'Publisher assigned successfully.']);
    }





    /********************************************************************
     * ADMIN WEBSITE CREATE AND ASSIGN PUBLISHER
     *********************************************************************
     * 
     * Create a new publisher and assign it to a website.
     * 
     * @param Request $request The HTTP request containing the publisher 
     * ...details
     * 
     * @return JsonResponse
     *********************************************************************/
    public function createAndAssignPublisher(Request $request)
    {

        // Validate
        $validated = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'website_id' => 'required|exists:marketplace_websites,id',
        ])->validate();


        $password = Str::random(10);
        // 1. Create publisher
        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'role' => Role::Publisher->value,
            'password' => bcrypt($password), // or random password
        ]);


        // 2. Assign to website
        MarketplaceAdminWebsite::where('id', $validated['website_id'])
            ->update(['publisher_user_id' => $user->id]);

        // send email notification to publisher with password

        $user->notify(new PublisherNewAccountNotification($password));

        return response()->json(['message' => 'Publisher created and assigned.', 'user' => $user]);
    }






    /********************************************************************
     * ADMIN WEBSITE SEARCH TOPICS
     *********************************************************************
     * 
     * Search for topics based on the search query.
     * 
     * @param Request $request The HTTP request containing the search query
     * @return JsonResponse
     ********************************************************************/
    public function searchTopics(Request $request)
    {
        $search = $request->input('q', '');
        $results = Topic::query()
            ->when($search, fn($query) => $query->where('name', 'like', "%{$search}%"))
            ->orderBy('name')
            ->limit(10)
            ->get();

        return response()->json($results);
    }






    /********************************************************************
     * ADMIN WEBSITE GET ACTIVITY LOGS
     *********************************************************************
     * 
     * Get the activity logs for a website.
     * 
     * @param MarketplaceAdminWebsite $website The website to get the logs for 
     * @param Request $request The HTTP request containing the logs
     * @return \Illuminate\Http\JsonResponse|\Inertia\Response
     ********************************************************************/
    public function getActivityLogs(MarketplaceAdminWebsite $website, Request $request)
    {

        // if user is outreach or publisher, hide the logs
        if (auth('web')->user()->role === Role::Outreach->value || auth('web')->user()->role === Role::Publisher->value) {
            return "";
        }

        /** @var \Illuminate\Pagination\LengthAwarePaginator $logs */
        $logs = Activity::with('causer')
            ->where('subject_type', MarketplaceAdminWebsite::class)
            ->where('subject_id', $website->id)
            ->latest()
            ->paginate(10);
        $logs->withQueryString();


        $logs->getCollection()->transform(function ($log) {
            // Add formatted date of the log itself
            $log->formatted_date = Carbon::parse($log->updated_at)->format('d M Y');

            // Format the updated_at inside properties.attributes and properties.old
            $props = $log->properties->toArray();

            foreach (['attributes', 'old'] as $section) {
                if (!isset($props[$section])) continue;

                foreach ($props[$section] as $key => $val) {
                    if (Str::endsWith($key, '_at') && $val) {
                        try {
                            $props[$section][$key] = Carbon::parse($val)->format('d M Y');
                        } catch (\Exception $e) {
                            // keep original if invalid
                        }
                    }
                }
            }

            $log->properties = collect($props);

            return $log;
        });

        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'props' => [
                    'logs' => $logs,
                ]
            ]);
        }

        return Inertia::render('Admin/Websites/WebsiteLogs', [
            'logs' => $logs,
            'website_id' => $website->id,
        ]);
    }






    /********************************************************************
     * ADMIN WEBSITE CHECK WEBSITE ELIGIBILITY
     *********************************************************************
     * 
     * Check if a website is eligible for publishing.
     * 
     * @param Request $request The HTTP request containing the website URL
     * @return JsonResponse
     ********************************************************************/
    public function checkWebsiteEligibility(Request $request): JsonResponse
    {

        $website_url = $request->input('website_url');

        // Normalize the URL
        if (!preg_match('/^https?:\/\//i', $website_url)) {
            $website_url = 'http://' . ltrim($website_url, '/');
        }

        // Check if dot is missing
        if (!str_contains($website_url, '.')) {
            return response()->json([
                'success' => false,
                'message' => 'The website URL is not valid.',
            ], 422);
        }

        // Run validation with the normalized URL
        $validator = Validator::make(['website_url' => $website_url], [
            'website_url' => ['required', 'url', new ValidWebsiteEligibility()],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first('website_url'),
            ], 422);
        }

        return response()->json([
            'success' => true,
            'message' => 'The website URL is valid.',
        ]);
    }






    /********************************************************************
     * ADMIN WEBSITE GENERATE SIGNED URL
     *********************************************************************
     * 
     * Generate a signed URL for a website.
     * 
     * @param MarketplaceAdminWebsite $website The website to generate the signed URL for
     * @return JsonResponse
     ********************************************************************/
    public function generateSignedUrl($id)
    {
        $website = MarketplaceAdminWebsite::findOrFail($id);
        $signedUrl = $website->generateSignedUrl();
        return response()->json(['url' => $signedUrl]);
    }







    /********************************************************************
     * ADMIN WEBSITE BULK IMPORT
     *********************************************************************
     * 
     * Import websites from a bulk import file.
     * 
     * @return \Inertia\Response
     ********************************************************************/
    public function bulkImport()
    {
        return Inertia::render('Admin/Websites/BulkImport');
    }






    /********************************************************************
     * ADMIN WEBSITE STORE BULK IMPORT
     *********************************************************************
     * 
     * Store websites from a bulk import file.
     * 
     * @param Request $request The HTTP request containing the websites
     * @return JsonResponse
     ********************************************************************/
    public function storeBulkImport(Request $request)
    {
        $rawLines = $request->input('websites', '');

        $lines = explode("\n", $rawLines);
        $inserted = 0;
        $skipped = 0;
        $seen = [];

        foreach ($lines as $line) {
            $clean = trim($line);
            if (!$clean) continue;

            $domain = $this->extractDomainName->extract($clean);

            if (!$domain || in_array($domain, $seen)) {
                $skipped++;
                continue;
            }

            $seen[] = $domain;

            if (MarketplaceWebsite::where('website_domain', $domain)->exists()) {
                $skipped++;
                continue;
            }

            MarketplaceWebsite::create([
                'website_domain' => $domain,
                'site_language_id' => 1,
                'main_category_id' => 1,
                'guest_post_price' => 0,
                'link_insert_price' => 0,
                'casino_post_price' => 0,
                'adult_post_price' => 0,
                'finance_post_price' => 0,
                'dating_post_price' => 0,
                'cbd_post_price' => 0,
                'crypto_post_price' => 0,
                'turn_around_time_in_days' => 0,
                'article_validity_in_months' => 36,
                'indexed_article' => true,
                'link_relation' => 'dofollow',
                'sponsorship_label' => false,
                'homepage_visible' => false,
                'active' => false,
                'site_source' => 'bulk-import',
            ]);

            $inserted++;
        }

        return response()->json([
            'success' => true,
            'message' => "{$inserted} websites imported. {$skipped} skipped.",
        ]);
    }





    /********************************************************************
     * ADMIN WEBSITE IMPORT CSV
     *********************************************************************
     * 
     * Import websites from a CSV file.
     * 
     * @param Request $request The HTTP request containing the CSV file
     * @return JsonResponse
     ********************************************************************/
    public function importCsv(Request $request)
    {

        $request->validate([
            'csv' => 'required|file|mimes:csv,txt',
        ]);

        $file = $request->file('csv');
        $handle = fopen($file->getRealPath(), 'r');
        $header = fgetcsv($handle);

        $expected = [
            'website_domain',
            'site_language_id',
            'main_category_id',
            'guest_post_price',
            'link_insert_price',
            'casino_post_price',
            'adult_post_price',
            'finance_post_price',
            'dating_post_price',
            'cbd_post_price',
            'crypto_post_price',
            'turn_around_time_in_days',
            'article_validity_in_months',
            'indexed_article',
            'link_relation',
            'sponsorship_label',
            'homepage_visible',
            'active',
            'site_source'
        ];

        // Default values just like storeBulkImport
        $defaults = [
            'site_language_id' => 1,
            'main_category_id' => 1,
            'guest_post_price' => 0,
            'link_insert_price' => 0,
            'casino_post_price' => 0,
            'adult_post_price' => 0,
            'finance_post_price' => 0,
            'dating_post_price' => 0,
            'cbd_post_price' => 0,
            'crypto_post_price' => 0,
            'turn_around_time_in_days' => 0,
            'article_validity_in_months' => 36,
            'indexed_article' => true,
            'link_relation' => 'dofollow',
            'sponsorship_label' => false,
            'homepage_visible' => false,
            'active' => false,
            'site_source' => 'csv-import',
        ];

        $inserted = 0;
        $skipped = 0;

        while (($row = fgetcsv($handle)) !== false) {
            $data = array_combine($header, $row);
            $domain = $this->extractDomainName->extract($data['website_domain'] ?? '');

            if (!$domain || MarketplaceWebsite::where('website_domain', $domain)->exists()) {
                $skipped++;
                continue;
            }

            // Clean and apply defaults for missing/empty values
            $fields = array_intersect_key($data, array_flip($expected));
            foreach ($expected as $field) {
                $fields[$field] = isset($fields[$field]) && $fields[$field] !== '' ? $fields[$field] : ($defaults[$field] ?? null);
            }

            $fields['website_domain'] = $domain;

            MarketplaceWebsite::create($fields);
            $inserted++;
        }

        return response()->json([
            'success' => true,
            'message' => "$inserted websites imported. $skipped skipped."
        ]);
    }
}
