<?php

namespace App\Http\Controllers\Admin;

use Inertia\Inertia;
use App\Models\MarketplacePayment;
use App\Http\Controllers\Controller;
use Domain\Payment\Payment;
use Domain\Payment\Requests\AdminPaymentFilterRequest;



class AdminPaymentController extends Controller
{

    /********************************************************************** 
     * ADMIN PAYMENT INDEX PAGE (REFACTOR - Too Big Code, Separate Logic ‼️)
     **********************************************************************
     * 
     * ‼️
     * - Break code into reusable components
     * - Make it easy to understand and follow through
     * - Query logic is poor, use chatgpt to learn how to write proper query
     * - Choice of words for comments is not good, we need few words that provide
     * .. maximum context without adding complexity.
     * 
     * 
     * Retrieves and filters marketplace payments with comprehensive 
     * ...search and filtering capabilities.
     * 
     * No Validation and poor queries
     * 
     * <PERSON>les complex querying of marketplace payments including:
     * - Eager loading of related user and order data
     * - Status-based filtering
     * - Date range filtering (preset and custom)
     * - Flexible search across multiple payment attributes
     * - Status-wise total calculations
     * - Sorting and pagination
     * 
     * @param \Illuminate\Http\Request $request The incoming HTTP 
     * ...request with query parameters
     * 
     * @return \Inertia\Response Renders the admin payments index 
     * ...page with filtered payment data
     *
     *********************************************************************/
    public function index(Request $request)
    {

        // -----------------------
        // Eager Payment Details
        // -----------------------
        // Initialize base query for marketplace payments with eager loaded relationships.
        // Loads user and order details, with order items count for additional context.
        $marketplace_payment_query = MarketplacePayment::with(['user', 'order'])
            ->withCount(['order' => function ($query) {
                $query->withCount('orderItems');
            }]);


        // -----------------------
        // Status Filter
        // -----------------------
        $marketplace_payment_query = $marketplace_payment_query->when($request->input('status'), 
                                            function ($query, $status) {
            return $query->where('status', $status);
        });



        // -----------------------
        // Filter By Date Range
        // -----------------------
        // Filters the query by a preset date range or custom start/end dates.
        // Supports preset ranges: today, yesterday, last 7/30/90 days, and last 12 months.
        // Falls back to custom date filtering using start_date and end_date inputs.
        // 
        // @param string $preset_range Optional preset date range selection
        // @param string $start_date Optional custom start date for filtering
        // @param string $end_date Optional custom end date for filtering
        $preset = $request->input('preset_range');
        $marketplace_payment_query = $marketplace_payment_query->when($preset, function ($query, $preset) use ($request) {
            if ($preset === 'today') {
                return $query->whereDate('created_at', today());
            } elseif ($preset === 'yesterday') {
                return $query->whereDate('created_at', today()->subDay());
            } elseif ($preset === 'last_7_days') {
                return $query->whereBetween('created_at', [now()->subDays(6)->startOfDay(), now()->endOfDay()]);
            } elseif ($preset === 'last_30_days') {
                return $query->whereBetween('created_at', [now()->subDays(29)->startOfDay(), now()->endOfDay()]);
            } elseif ($preset === 'last_90_days') {
                return $query->whereBetween('created_at', [now()->subDays(89)->startOfDay(), now()->endOfDay()]);
            } elseif ($preset === 'last_12_months') {
                return $query->whereBetween('created_at', [now()->subMonths(12)->startOfDay(), now()->endOfDay()]);
            } else {
                if ($start = $request->input('start_date')) {
                    return $query->whereDate('created_at', '>=', $start);
                }
                if ($end = $request->input('end_date')) {
                    return $query->whereDate('created_at', '<=', $end);
                }
            }
        });


        // -----------------------
        // Apply Search Filter
        // -----------------------
        // Applies a flexible search filter to the marketplace payments query.
        // Searches across payment ID, amount, Stripe payment intent, associated user details,
        // and order ID using a single search term with multiple matching strategies.
        //
        // @param string $search The search term to match against various payment attributes
        $marketplace_payment_query = $marketplace_payment_query->when($search = $request->input('search'), function ($query, $search) {
            return $query->where(function ($payment_sub_query) use ($search) {
                $payment_sub_query->where('id', $search)
                    ->orWhere('payment_amount', 'like', "%$search%")
                    ->orWhere('external_transaction_id', 'like', "%$search%")
                    ->orWhereHas('user', function ($payment_sub_query) use ($search) {
                        $payment_sub_query->where('name', 'like', "%$search%")
                            ->orWhere('email', 'like', "%$search%");
                    })
                    ->orWhereHas('order', function ($payment_sub_query) use ($search) {
                        $payment_sub_query->where('id', $search);
                    });
            });
        });


        // -----------------------
        // Status-wise Sum Clone
        // -----------------------
        // Clones the filtered payments query to calculate total amounts
        // grouped by each payment status (e.g., completed, pending, failed).
        $statusQuery = clone $marketplace_payment_query;
        $statuses = ['paid', 'pending', 'refunded', 'partially_refunded', 'credit_applied'];
        $totals = [];

        $totals = $statusQuery->whereIn('status', $statuses)
            ->select('status')
            ->selectRaw('SUM(payment_amount) as total')
            ->groupBy('status')
            ->pluck('total', 'status')
            ->toArray();

        // Clone the filtered payments query for the grand total (NO whereIn)
        $grandTotalQuery = clone $marketplace_payment_query;
        $grandTotal = $grandTotalQuery->sum('payment_amount');

        $totals['total'] = $grandTotal;


        // -----------------------
        // Apply Sorting
        // -----------------------
        // Sorts the payments query based on the selected column and direction.
        //
        // @param string $sortBy The column to sort by
        // @param string $sortDir The direction to sort by
        $sortBy = $request->input('sort_by', 'id');
        $sortDir = $request->input('sort_dir', 'desc');


        if (in_array($sortBy, ['id', 'created_at', 'user_id', 'payment_amount', 'status', 'external_transaction_id'])) {
            $marketplace_payment_query->orderBy($sortBy, $sortDir);
        }


        // -----------------------
        // Pagination
        // -----------------------
        /** @var \Illuminate\Pagination\LengthAwarePaginator $payments */
        $payments = $marketplace_payment_query->paginate(15);
        $payments->withQueryString();


        return Inertia::render('Admin/Payments/Index', [
            'payments' => $payments,
            'totals' => $totals,
            'filters' => [
                'search' => $search,
                'sort_by' => $sortBy,
                'sort_dir' => $sortDir,
                'status' => $request->input('status'),
                'preset_range' => $preset,
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ],
        ]);
    }





    /**********************************************************************
     * ADMIN PAYMENT SHOW PAGE
     **********************************************************************
     * 
     * Retrieves and displays detailed payment information including 
     * ...user and order details.
     * 
     * Loads user and order data with eager loading of order items count.
     * 
     * @param MarketplacePayment $payment The payment to display details for
     * @return \Inertia\Response Renders the admin payment show page with 
     * ...payment details
     *
     *********************************************************************/
    public function show(MarketplacePayment $payment)
    {

        $payment = $payment->load(
            [
                'user',
                'order' => fn($order_query): mixed => $order_query->withCount('orderItems')
            ]
        );


        return Inertia::render('Admin/Payments/Show', [
            'payment' => $payment,
        ]);
    }
}
