<?php

namespace App\Http\Controllers\Publisher;

use Inertia\Inertia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use App\Http\Controllers\Controller;
use App\Models\MarketplaceAdminWebsite;
use App\Http\Requests\AdminWebsiteStoreRequest;
use App\Http\Requests\AdminWebsiteUpdateRequest;
use App\Services\Publisher\PublisherWebsitesService;
use App\Http\Controllers\MarketplaceMetaDataController;
use Domain\Website\List\getFilteredWebsites;
use Domain\Website\Traits\WebsiteActionTrait;
use Illuminate\Support\Collection;

class PublisherWebsiteController extends Controller
{
    use WebsiteActionTrait;

    private readonly Collection $categories;
    private readonly Collection $languages;
    public function __construct(
        private PublisherWebsitesService $publisherWebsitesService,
    ) {
        $this->categories = MarketplaceMetaDataController::categoriesList();
        $this->languages = MarketplaceMetaDataController::languagesList();
    }

    /*********************************************************************
     * INDEX WEBSITES
     **********************************************************************
     *
     * Displays a list of all websites for the publisher.
     *
     * @param Request $request
     * @return \Inertia\Response
     *
     *********************************************************************/
    public function index(Request $request, getFilteredWebsites $getFilteredWebsites)
    {

        //-----------------------
        // Filters
        //-----------------------
        $filters = [
            'searchTerm' => $request->input('searchTerm'),
            'status'     => $request->input('status'),
            'sortField'  => $request->input('sortField', 'id'),
            'sortOrder'  => $request->input('sortOrder', 'desc'),
            'perPage'    => $request->input('perPage', 10),
        ];


        $websites = $getFilteredWebsites->getFiltered($filters);


        // -----------------------
        // Render
        // -----------------------
        return Inertia::render('Publisher/Websites/Index', [
            'websites' => $websites,
            'filters' => $filters,
        ]);
    }





    /*********************************************************************
     * CREATE WEBSITE
     **********************************************************************
     *
     * Creates a new website for the publisher.
     *
     * @return \Inertia\Response
     *
     *********************************************************************/
    public function create()
    {


        return Inertia::render('Publisher/Websites/Form', [
            'mode' => 'create',
            'categories' => $this->categories,
            'languages' => $this->languages,
        ]);
    }





    /*********************************************************************
     * STORE WEBSITE
     **********************************************************************
     *
     * Stores a new website for the publisher.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * 
     *********************************************************************/
    public function store(AdminWebsiteStoreRequest $request)
    {
        $result = $this->storeWebsite($request->validated());

        if ($result) {
            return redirect()->route('publisher.websites.edit', $result->id)
                ->with('success', 'Website created successfully.');
        }

        return redirect()->back()->with('error', 'Failed to create website.');
    }





    /*********************************************************************
     * EDIT WEBSITE
     **********************************************************************
     *
     * Edits a website for the publisher.
     *
     * @param MarketplaceAdminWebsite $website
     * @return \Inertia\Response
     *
     *********************************************************************/
    public function edit(MarketplaceAdminWebsite $website)
    {


        return Inertia::render('Publisher/Websites/Form', [
            'mode' => 'edit',
            'website' => $website,
            'categories' => $this->categories,
            'languages' => $this->languages,
        ]);
    }





    /*********************************************************************
     * UPDATE WEBSITE
     **********************************************************************
     *
     * Updates a website for the publisher.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse    
     *
     *********************************************************************/
    public function update(AdminWebsiteUpdateRequest $request, MarketplaceAdminWebsite $website)
    {
        $result = $this->updateWebsite($request->validated(), $website);


        if ($result) {
            return redirect()->route('publisher.websites.edit', $result->id)
                ->with('success', 'Website updated successfully.');
        }

        return redirect()->back()->with('error', 'Failed to update website.');
    }





    /*********************************************************************
     * CHECK DOMAIN
     **********************************************************************
     *
     * Checks if a domain is already taken and sends verification code.
     *
     * @param Request $request  
     * @return \Illuminate\Http\JsonResponse
     *
     *********************************************************************/
    public function validateDomain(Request $request)
    {
        $this->publisherWebsitesService->validateDomain($request);


        if ($request->mode === 'email') {
            // Send verification code
            $result = $this->publisherWebsitesService->sendVerificationCode($request->domain, $request->email);

            return response()->json($result);
        }
    }





    /*********************************************************************
     * VERIFY DOMAIN
     **********************************************************************
     *
     * Verifies a domain by checking the verification code.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse    
     *
     *********************************************************************/
    public function verifyDomain(Request $request)
    {
        $request->validate([
            'domain' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'verification_code' => 'required|string|max:255',
        ]);

        $result = $this->publisherWebsitesService->verifyDomain($request->domain, $request->email, $request->verification_code);

        return response()->json($result);
    }





    /*********************************************************************
     * DISPLAY WEBSITE DETAILS
     *********************************************************************
     *
     * Displays the detailed information of a website for editing.
     * Retrieves the website data and associated topic names.
     *
     * @param int $id
     * The ID of the website to retrieve
     *
     * @return \Inertia\Response
     * The Inertia response for the edit website view
     *
     *********************************************************************/
    public function signedEditWebsite($id)
    {
        $website = MarketplaceAdminWebsite::findOrFail($id);


        $signedUrl = URL::temporarySignedRoute(
            'publisher.websites.update.signed',
            now()->addMinutes(10),
            ['id' => $website->id]
        );


        return Inertia::render('Publisher/Websites/Form', [
            'mode' => 'edit',
            'signed' => $signedUrl,
            'website' => $website,
            'categories' => $this->categories,
            'languages' => $this->languages,
            'topics' => $website->topics->pluck('name'),
        ]);
    }





    /*********************************************************************
     * UPDATE WEBSITE
     **********************************************************************
     *
     * Updates a website for the publisher.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse    
     * 
     *********************************************************************/
    public function signedUpdateWebsite(AdminWebsiteUpdateRequest $request)
    {
        $website = MarketplaceAdminWebsite::findOrFail($request->id);

        $this->updateWebsite($request->validated(), $website);

        return $this->signedEditWebsite($request->id);
    }





    /*********************************************************************
     * TOGGLE WEBSITE STATUS
     **********************************************************************
     *
     * Toggles the active status of a website for the publisher.
     *
     * @param Request $request
     * @param MarketplaceAdminWebsite $website
     * @return \Illuminate\Http\RedirectResponse
     *
     *********************************************************************/
    public function toggleStatus(Request $request, MarketplaceAdminWebsite $website)
    {
        $this->publisherWebsitesService->toggleStatus($request, $website);

        return back()->with('success', 'Website status updated successfully.');
    }
}
