<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;


class TestController extends Controller
{
    public function __construct()
    {
        if (env('APP_ENV') !== 'local') {
            abort(404);
        }
    }

    public function test()
    {


        // $order = $this->order->create([
        //     'payment_intent_id' => 'pi_3Rl6ZfQ1hFv0wmi307nQibrv',
        //     'user_id' => 4,
        // ]);
        // return $order;
    }
}
