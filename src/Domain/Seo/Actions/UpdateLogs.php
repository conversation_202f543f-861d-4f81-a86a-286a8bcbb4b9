<?php

namespace Domain\Seo\Actions;

use App\Models\MarketplaceWebsite;

/*********************************************************************
 * UPDATE SEO LOGS ACTION
 *********************************************************************
 *
 * Handles the updating of SEO statistics logs for marketplace websites.
 * This action manages the logging of SEO data fetching operations and
 * their results for monitoring and debugging purposes.
 *
 * Responsibilities:
 * - Updates or creates SEO logs for websites
 * - Tracks SEO data fetching attempts and results
 * - Maintains audit trail of SEO operations
 * - Supports monitoring and debugging of SEO processes
 *
 *********************************************************************/
class UpdateLogs
{
    /*********************************************************************
     * UPDATE OR CREATE SEO LOGS
     *********************************************************************
     *
     * Updates existing SEO logs or creates new ones for a website.
     * This method ensures that SEO operation logs are properly maintained
     * for tracking and debugging purposes.
     *
     * @param MarketplaceWebsite $website - The website to update logs for
     * @param array $data - Log data including status, attempts, timestamps
     * @return void
     *
     *********************************************************************/
    public function __invoke(MarketplaceWebsite $website, array $data)
    {
        $website->seoStatsLogs()->updateOrCreate(
            [
                'marketplace_website_id' => $website->getKey(),
            ],
            $data
        );
    }
}
