<?php

namespace Domain\Payment\Enum;

enum PaymentStatus: string
{
    case Paid = 'paid';
    case Pending = 'pending';
    case Refunded = 'refunded';
    case PartiallyRefunded = 'partially_refunded';
    case CreditApplied = 'credit_applied';
    case Failed = 'failed';

    /*********************************************************************
     * GET ALL STATUS VALUES
     *********************************************************************
     *
     * Returns array of all payment status values for validation
     * and filtering purposes.
     *
     * @return array
     *
     *********************************************************************/
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /*********************************************************************
     * GET FILTERABLE STATUSES
     *********************************************************************
     *
     * Returns array of statuses used in admin filtering
     * (excludes 'failed' as it's not used in current admin interface)
     *
     * @return array
     *
     *********************************************************************/
    public static function filterableValues(): array
    {
        return [
            self::Paid->value,
            self::Pending->value,
            self::Refunded->value,
            self::PartiallyRefunded->value,
            self::CreditApplied->value,
        ];
    }
}
