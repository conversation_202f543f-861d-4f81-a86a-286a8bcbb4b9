<?php

namespace Domain\Payment;

use App\Models\MarketplacePayment;
use Domain\Payment\List\GetPayment;
use Domain\Payment\List\GetPaymentsWithFilters;
use Domain\Payment\List\GetPaymentStatusTotals;
use Domain\Payment\Action\CreatePayment;
use Domain\Payment\Resources\AdminPaymentResource;
use Domain\Payment\Stripe\GetPaymentByIntentID as StripePaymentStatus;
use Domain\Payment\Stripe\CreatePaymentIntent as StripeCreatePaymentIntent;
use App\Models\User;
use Domain\Payment\Stripe\Webhook\processOrder;
use Illuminate\Pagination\LengthAwarePaginator;

class Payment
{
    use processOrder;

    public function __construct() {}


    /*********************************************************************
     * CREATE PAYMENT - PROCESS PAYMENT CREATION
     *********************************************************************
     *
     * Handles payment creation by delegating to the CreatePayment action.
     * - Validates and processes payment data
     * - Creates payment record in database
     * - Returns created payment instance
     *
     * @param array $paymentData - Payment information array containing user_id,
     * ..payment_intent_id, total_amount, and status
     *
     * @return MarketplacePayment - Created payment record with all details
     *
     *********************************************************************/
    public function create(array $paymentData)
    {
        // -----------------------
        // Process Payment Creation
        // Delegates to CreatePayment action to handle database operations
        return (new CreatePayment())->handle($paymentData);
    }

    /*********************************************************************
     * GET PAYMENT - GET PAYMENT BY PAYMENT INTENT ID
     *********************************************************************
     *
     * Gets a payment by its payment intent ID.
     *
     *********************************************************************/
    public function getPayment(string $paymentIntentId): MarketplacePayment|null
    {
        return (new GetPayment())->getPayment($paymentIntentId);
    }

    /*********************************************************************
     * GET PAYMENT STATUS - GET PAYMENT STATUS BY PAYMENT INTENT ID
     *********************************************************************
     *
     * Gets a payment status by its payment intent ID.
     *
     *********************************************************************/
    public function getPaymentStatus(string $paymentIntentId, string $paymentMethod): object
    {
        // -----------------------
        // Get payment status
        switch ($paymentMethod) {
            case 'stripe':
                return (new StripePaymentStatus())->handle($paymentIntentId);
            default:
                return (new StripePaymentStatus())->handle($paymentIntentId);
        }
    }

    /*********************************************************************
     * CREATE PAYMENT INTENT
     *********************************************************************
     *
     * Creates a payment intent for the user.
     *
     *********************************************************************/
    public function createPaymentIntent(string $amount, User $user, string $paymentMethod): object | bool
    {
        switch ($paymentMethod) {
            case 'stripe':
                return (new StripeCreatePaymentIntent())->handle($amount, $user);
            default:
                return (new StripeCreatePaymentIntent())->handle($amount, $user);
        }
    }
}
