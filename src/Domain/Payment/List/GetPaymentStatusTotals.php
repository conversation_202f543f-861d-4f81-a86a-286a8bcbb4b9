<?php

namespace Domain\Payment\List;

use App\Models\MarketplacePayment;
use App\Traits\Filters\DateFilterTrait;
use Domain\Payment\Enum\PaymentStatus;
use Illuminate\Database\Eloquent\Builder;

/*********************************************************************
 * GET PAYMENT STATUS TOTALS
 *********************************************************************
 *
 * Calculates payment totals grouped by status with efficient
 * single-query approach. Supports date filtering and provides
 * both status-wise totals and grand total.
 *
 * Responsibilities:
 * - Calculate status-wise payment totals
 * - Apply date filtering to totals calculation
 * - Ensure all payment statuses are represented
 * - Calculate grand total across all statuses
 * - Return formatted totals array
 *
 *********************************************************************/
class GetPaymentStatusTotals
{
    use DateFilterTrait;


    /*********************************************************************
     * GET STATUS TOTALS
     *********************************************************************
     *
     * Calculates payment totals grouped by status using optimized
     * single query approach with date filtering support.
     *
     * @param Builder $baseQuery - Base query with applied filters
     * @param array $filters - Date filter parameters
     * @return array - Status totals with grand total
     *
     *********************************************************************/
    public function __invoke(Builder $baseQuery, array $filters): array
    {
        // -----------------------
        // Clone Query For Totals Calculation
        $totalsQuery = clone $baseQuery;


        // -----------------------
        // Calculate Status-Wise Totals
        $statusTotals = $totalsQuery
            ->whereIn('status', PaymentStatus::filterableValues())
            ->select('status')
            ->selectRaw('SUM(payment_amount) as total')
            ->groupBy('status')
            ->pluck('total', 'status')
            ->toArray();


        // -----------------------
        // Calculate Grand Total
        $grandTotalQuery = clone $baseQuery;
        $grandTotal = $grandTotalQuery->sum('payment_amount');


        // -----------------------
        // Ensure All Statuses Are Present
        $totals = [];
        foreach (PaymentStatus::filterableValues() as $status) {
            $totals[$status] = $statusTotals[$status] ?? 0;
        }


        // -----------------------
        // Add Grand Total
        $totals['total'] = $grandTotal;

        return $totals;
    }


    /*********************************************************************
     * GET TOTALS WITH FRESH QUERY
     *********************************************************************
     *
     * Alternative method that creates a fresh query with the same
     * filters applied. Useful when you need totals independently.
     *
     * @param array $filters - All filter parameters
     * @return array - Status totals with grand total
     *
     *********************************************************************/
    public function getWithFreshQuery(array $filters): array
    {
        // -----------------------
        // Create Base Query
        $baseQuery = MarketplacePayment::query();


        // -----------------------
        // Apply Date Filter
        $baseQuery = $this->applyDateFilter($baseQuery, $filters);


        // -----------------------
        // Apply Status Filter (if any)
        if (!empty($filters['status'])) {
            $baseQuery->status($filters['status']);
        }


        // -----------------------
        // Apply Search Filter (if any)
        if (!empty($filters['searchTerm'])) {
            $baseQuery->search($filters['searchTerm']);
        }


        // -----------------------
        // Calculate Totals
        return $this($baseQuery, $filters);
    }
}
