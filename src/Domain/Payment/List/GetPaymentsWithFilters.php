<?php

namespace Domain\Payment\List;

use App\Models\MarketplacePayment;
use App\Traits\Filters\DateFilterTrait;
use Illuminate\Pagination\LengthAwarePaginator;

class GetPaymentsWithFilters
{
    use DateFilterTrait;


    /*********************************************************************
     * GET FILTERED PAYMENTS
     **********************************************************************
     * Retrieves payments with filters, sorting, and pagination.
     *
     * @param array $filters
     * @return LengthAwarePaginator
     *********************************************************************/
    public function __invoke(array $filters): LengthAwarePaginator
    {
        return MarketplacePayment::query()
            ->with(['user', 'order'])
            ->withCount(['order' => function ($q) {
                $q->withCount('orderItems');
            }])

            // -----------------------
            // Apply date filter only when date params are present
            ->when(
                isset($filters['date_from']) || isset($filters['date_to']),
                fn($q) => $this->applyDateFilter($q, $filters)
            )

            // -----------------------
            // Apply status scope only when provided
            ->when(
                filled($filters['status']),
                fn($q, $status) => $q->status($status),
                fn($q) => $q
            )

            // -----------------------
            // Apply search scope only when provided
            ->when(
                filled($filters['searchTerm']),
                fn($q, $term) => $q->search($term),
                fn($q) => $q
            )

            // -----------------------
            // Apply sorting (always)
            ->sort(
                $filters['sortField']  ?? 'id',
                $filters['sortOrder']  ?? 'desc'
            )

            // -----------------------
            // Paginate & preserve query string
            ->paginate(
                $filters['perPage']    ?? config('pressbear.default_pagination_10', 15)
            )
            ->withQueryString();
    }
}
