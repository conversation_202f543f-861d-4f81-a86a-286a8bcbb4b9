<?php

declare(strict_types=1);

namespace Pressbear\Commands;

use App\Models\MarketplaceWebsite;
use Domain\Seo\Enums\SEOSourceType;
use Domain\Seo\Jobs\FetchWebsiteSeo;
use Illuminate\Console\Command;


class FetchSeo extends Command
{
    protected $signature = 'seo:fetch {--source= : The source of the SEO data} {--type= : The type of the SEO data} {--limit= : The limit of the websites to fetch}';

    protected $description = 'Fetch SEO data for a website';


    /*******************************************************************
     * FETCH SEO DATA COMMAND HANDLER
     *********************************************************************
     *
     * Processes SEO data fetching for marketplace websites.
     * Dispatches jobs to fetch SEO statistics from external sources
     * ..for websites that don't have recent SEO data or have expired data.
     *
     * - Validates and prompts for SEO source and data type if not provided
     * - Filters websites that need SEO data updates
     * - Dispatches individual jobs for each website to fetch SEO data
     *
     * @return void
     * Dispatches SEO fetch jobs and outputs success message
     *
     *******************************************************************/
    public function handle(): void
    {
        // -----------------------
        // Initialize SEO Sources and Types
        $seoSources = array_column(SEOSourceType::cases(), 'value');
        $seoDataTypes = ['domain', 'keyword'];


        // -----------------------
        // Get User Input for Source and Type
        $selectedSource = $this->option('source') ?? $this->choice(
            'Please provide the SEO data source (e.g., similarweb, semrush)',
            $seoSources,
            'similarweb'
        );
        $selectedType = $this->option('type') ?? $this->choice(
            'What type of SEO data would you like to fetch?',
            $seoDataTypes,
            'domain'
        );


        // -----------------------
        // Fetch Websites Needing SEO Data
        $query = MarketplaceWebsite::needsSeoStatsUpdate($selectedSource, $selectedType);

        $websitesNeedingSeoData = $query->clone()
            ->limit($this->option('limit') ?? config('pressbear.seo_fetch_limit'))
            ->get();


        // -----------------------
        // Process Each Website
        $sourceTypeEnum = SEOSourceType::from($selectedSource);

        foreach ($websitesNeedingSeoData as $website) {
            FetchWebsiteSeo::dispatch($website, $sourceTypeEnum, $selectedType);
        }


        // -----------------------
        // Output Success Message
        $this->info($websitesNeedingSeoData->count() . ' jobs dispatched successfully. and ' . $query->clone()->count() . ' websites found.');
    }
}
