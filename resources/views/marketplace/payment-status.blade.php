<x-app-layout>
    <x-app.header />



    {{-- Main Body Wrapper --}}
    <div id="app-body" class="flex justify-center">


        <div id="page-main-wrapper" class=" bg-gray-50 my-8 w-full md:w-1/2 h-auto rounded-lg mx-auto max-w-full 
                px-12 py-14 sm:px-8 
               lg:max-w-screen-2xl lg:px-16  ">


            <div class="pb-8 border-gray-300 flex flex-col items-center justify-center">


                <img class="h-64" src="{{ storage::url('graphics/illustrations/success.png') }}">

                <div class="mt-2 text-lg font-bold text-emerald-700 p-4 flex">
                    <span class="h-6 w-6 mt-0.5">
                        <x-icons.lucide.check-circle />
                    </span>
                    <span id="payment-message" class="ml-2">
                        Payment Success
                    </span>
                </div>


                {{-- Message --}}
                <div class="mb-8 mt-2 text-center text-gray-800">
                    <p class="mb-1">
                        Your Payment was successful.
                    </p>
                    <p class="">
                        Redirecting you to the order page in few seconds.
                    </p>
                </div>


                {{-- Action Button --}}
                <a class="my-4" href="{{route('advertiser.order-details', $orderId ?? '')}}" wire:navigate>
                    <button type="button"
                        class=" px-4 py-2 text-sm font-medium text-white rounded-lg bg-orange-600 hover:bg-orange-700  dark:bg-primary dark:hover:bg-primary focus:outline-none  ">
                        Go To My Order
                    </button>
                </a>

            </div>
        </div>



        {{-- Javascript redirect to order page --}}
        <script type="text/javascript">
            window.setTimeout(function(){

            // Move to a new location or you can do something else
            window.location.href = "{{route('advertiser.order-details', $orderId ?? '')}}"; 

        }, 3000);
        </script>


        {{-- Stripe Form --}}
        {{-- <form id="stripe-form" data-secret="{{$clientSecret}}">
            <div id="stripe-form-auto" class="mt-2 text-lg font-bold text-emerald-700 p-4">
                <div id="payment-message" class="hide-content ">
                    Stripe.js injects the Payment Message
                </div>
        </form> --}}



        {{-- Body Main Wrapper End --}}
    </div>
</x-app-layout>