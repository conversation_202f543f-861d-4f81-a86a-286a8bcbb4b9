<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PaymentSettingsController;
use App\Http\Controllers\Publisher\OrderController;
use App\Http\Controllers\Publisher\PublisherWebsiteController;
use App\Http\Controllers\Publisher\PublisherController;
use App\Http\Controllers\Auth\RegisteredPublisherController;


/****************************************************************************************************/
//------------------------------------  PUBLISHER ROUTES--------------------------------------------//
/****************************************************************************************************/
Route::middleware('can:publisher')->group(function (): void {



        //--------------------------------------------------------------------------------//
        //                                      GENERAL                                   //
        //--------------------------------------------------------------------------------//
        Route::get('/', [PublisherController::class, 'dashboard'])->name('dashboard');
        Route::get('/settings', [PublisherController::class, 'settings'])->name('settings.index');



        //--------------------------------------------------------------------------------//
        //                                  ORDER MANAGEMENT                              //
        //--------------------------------------------------------------------------------//
        Route::get('/orders', [OrderController::class, 'orders'])->name('orders.index');
        Route::get('/order/details/{item}', [OrderController::class, 'orderDetails'])->name('orders.details');



        //--------------------------------------------------------------------------------//
        //                            PUBLISHER WEBSITE MANAGEMENT                        //
        //--------------------------------------------------------------------------------//
        Route::post('/websites/validate', [PublisherWebsiteController::class, 'validateDomain'])
                ->name('websites.validate'); //domain validation

        Route::post('/websites/verify', [PublisherWebsiteController::class, 'verifyDomain'])
                ->name('websites.verify'); //website eligibility checks

        Route::put('/websites/{website}/toggle-status', [PublisherWebsiteController::class, 'toggleStatus'])
                ->name('websites.toggle-status'); //website active state

        /**
         * Full CRUD operations for publisher websites including:
         * - GET /websites (index) - List all websites
         * - GET /websites/create (create) - Show create form
         * - POST /websites (store) - Save new website
         * - GET /websites/{id} (show) - Display specific website
         * - GET /websites/{id}/edit (edit) - Show edit form
         * - PUT/PATCH /websites/{id} (update) - Update website
         * - DELETE /websites/{id} (destroy) - Delete website
         */
        Route::resource('websites', PublisherWebsiteController::class);



        //--------------------------------------------------------------------------------//
        //                                  PAYMENT + WALLET                              //
        //--------------------------------------------------------------------------------//
        Route::get('/wallet', [PublisherController::class, 'wallet'])->name('wallet.index');
        Route::get('/wallet/transactions/{id}', [PublisherController::class, 'walletTransactions'])
                ->name('transactions.details');

        Route::get('/payments', [PublisherController::class, 'payments'])->name('payments.index');
        Route::get('/settings/payment-settings', [PaymentSettingsController::class, 'index'])
                ->name('payment-settings.index');
});



//--------------------------------------------------------------------------------//
//                               GUEST: ORDER MANAGEMENT ⚠️                       //
//--------------------------------------------------------------------------------//
// These routes are supposed to be signed routes for order and website update without login requirement ‼️
// how is auth managed for them.
// Merge these action in single route ‼️
Route::prefix('orders')->name('orders.item.')->group(function () {

        Route::post('/item/{item}/approve', [OrderController::class, 'approveOrderItem'])
                ->name('approve-requirement');
        Route::post('/item/{item}/request-revision', [OrderController::class, 'requestRevision'])
                ->name('request-revision');
        Route::post('/item/{item}/cancel', [OrderController::class, 'cancelOrderItem'])
                ->name('cancel');

        // Content Revision Routes
        Route::post('/item/{item}/request-content-revision', [OrderController::class, 'requestContentRevision'])
                ->name('request-content-revision');
        Route::post('/item/{item}/approve-content', [OrderController::class, 'approveContent'])
                ->name('approve-content');
        Route::post('/item/{item}/publish', [OrderController::class, 'publish'])
                ->name('publish');
});



//--------------------------------------------------------------------------------//
//                                PUBLISHER REGISTRATION                          //
//--------------------------------------------------------------------------------//
Route::middleware('guest')->group(function () {
        Route::get('/register', [RegisteredPublisherController::class, 'create'])->name('register');
        Route::post('/register', [RegisteredPublisherController::class, 'store'])
                ->middleware('throttle:60,1')
                ->name('register.store');
});
